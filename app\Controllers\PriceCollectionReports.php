<?php

namespace App\Controllers;

use App\Models\ActivityPriceCollectionDataModel;
use App\Models\GoodsGroupModel;
use App\Models\GoodsBrandModel;
use App\Models\GoodsItemModel;
use App\Models\BusinessEntityModel;
use App\Models\BusinessLocationModel;
use App\Models\GeoDistrictModel;
use App\Models\GeoProvinceModel;
use App\Models\UserModel;

class PriceCollectionReports extends BaseController
{
    protected $priceDataModel;
    protected $goodsGroupModel;
    protected $goodsBrandModel;
    protected $goodsItemModel;
    protected $businessEntityModel;
    protected $businessLocationModel;
    protected $geoDistrictModel;
    protected $geoProvinceModel;
    protected $userModel;

    public function __construct()
    {
        $this->priceDataModel = new ActivityPriceCollectionDataModel();
        $this->goodsGroupModel = new GoodsGroupModel();
        $this->goodsBrandModel = new GoodsBrandModel();
        $this->goodsItemModel = new GoodsItemModel();
        $this->businessEntityModel = new BusinessEntityModel();
        $this->businessLocationModel = new BusinessLocationModel();
        $this->geoDistrictModel = new GeoDistrictModel();
        $this->geoProvinceModel = new GeoProvinceModel();
        $this->userModel = new UserModel();
    }

    /**
     * Check admin authentication
     */
    private function checkAdminAuth()
    {
        if (!session()->get('logged_in') || 
            (session()->get('is_admin') != 1 && session()->get('is_supervisor') != 1)) {
            return redirect()->to('admin')->with('error', 'Please login to access admin portal.');
        }
        return null;
    }

    /**
     * GET: admin/price-collection-reports/raw
     * Display raw price collection data for the last 12 months
     */
    public function raw()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('org_id');
        
        // Get price data for the last 12 months
        $rawData = $this->getRawPriceData($orgId);

        $data = [
            'title' => 'Raw Price Collection Report',
            'rawData' => $rawData
        ];

        return view('price_collection_reports/price_collection_reports_raw', $data);
    }

    /**
     * Get raw price collection data with all related information
     */
    private function getRawPriceData($orgId)
    {
        // Calculate date 12 months ago
        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));

        // Get price data with joins
        $priceData = $this->priceDataModel
            ->select('
                activity_price_collection_data.id,
                activity_price_collection_data.price,
                activity_price_collection_data.created_at,
                activity_price_collection_data.updated_at,
                goods_groups.group_name,
                goods_brands.brand_name,
                goods_brands.type as brand_type,
                goods_items.item as item_name,
                business_locations.business_name as location_name,
                business_locations.type as business_type,
                business_entities.business_name as entity_name,
                geo_districts.name as district_name,
                geo_provinces.name as province_name,
                created_user.name as created_by_name,
                updated_user.name as updated_by_name
            ')
            ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
            ->join('goods_brands', 'goods_brands.id = goods_items.goods_brand_id', 'left')
            ->join('goods_groups', 'goods_groups.id = goods_brands.goods_group_id', 'left')
            ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
            ->join('business_entities', 'business_entities.id = business_locations.business_entity_id', 'left')
            ->join('geo_districts', 'geo_districts.id = business_locations.district_id', 'left')
            ->join('geo_provinces', 'geo_provinces.id = business_locations.province_id', 'left')
            ->join('users as created_user', 'created_user.id = activity_price_collection_data.created_by', 'left')
            ->join('users as updated_user', 'updated_user.id = activity_price_collection_data.updated_by', 'left')
            ->where('activity_price_collection_data.org_id', $orgId)
            ->where('activity_price_collection_data.created_at >=', $twelveMonthsAgo)
            ->where('activity_price_collection_data.is_deleted', false)
            ->orderBy('activity_price_collection_data.id', 'DESC')
            ->findAll();

        return $priceData;
    }
}
