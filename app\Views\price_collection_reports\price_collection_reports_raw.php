<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>Raw Price Collection Report
                            </h2>
                            <p class="text-muted mb-0">View all price collection data from the last 12 months</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary">
                                <i class="bi bi-house me-1"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-table me-2"></i>Price Collection Data
                        <span class="badge bg-primary ms-2"><?= count($rawData) ?> Records</span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($rawData)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Goods Group</th>
                                        <th>Brand</th>
                                        <th>Item</th>
                                        <th>Brand Type</th>
                                        <th>Price</th>
                                        <th>Business Type</th>
                                        <th>Business Location</th>
                                        <th>Business Entity</th>
                                        <th>District</th>
                                        <th>Province</th>
                                        <th>Created Date</th>
                                        <th>Updated Date</th>
                                        <th>Created By</th>
                                        <th>Updated By</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $counter = 1; ?>
                                    <?php foreach ($rawData as $row): ?>
                                        <tr>
                                            <td><?= $counter++ ?></td>
                                            <td><?= esc($row['group_name'] ?? 'N/A') ?></td>
                                            <td><?= esc($row['brand_name'] ?? 'N/A') ?></td>
                                            <td><?= esc($row['item_name'] ?? 'N/A') ?></td>
                                            <td>
                                                <span class="badge bg-<?= $row['brand_type'] === 'primary' ? 'primary' : 'secondary' ?>">
                                                    <?= ucfirst($row['brand_type'] ?? 'N/A') ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <strong><?= number_format($row['price'] ?? 0, 2) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $row['business_type'] === 'retail' ? 'info' : 'warning' ?>">
                                                    <?= ucfirst($row['business_type'] ?? 'N/A') ?>
                                                </span>
                                            </td>
                                            <td><?= esc($row['location_name'] ?? 'N/A') ?></td>
                                            <td><?= esc($row['entity_name'] ?? 'N/A') ?></td>
                                            <td><?= esc($row['district_name'] ?? 'N/A') ?></td>
                                            <td><?= esc($row['province_name'] ?? 'N/A') ?></td>
                                            <td>
                                                <small><?= date('M d, Y H:i', strtotime($row['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <small><?= $row['updated_at'] ? date('M d, Y H:i', strtotime($row['updated_at'])) : 'N/A' ?></small>
                                            </td>
                                            <td><?= esc($row['created_by_name'] ?? 'N/A') ?></td>
                                            <td><?= esc($row['updated_by_name'] ?? 'N/A') ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                            <h4 class="text-muted mt-3">No Price Data Found</h4>
                            <p class="text-muted">No price collection data available for the last 12 months.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
